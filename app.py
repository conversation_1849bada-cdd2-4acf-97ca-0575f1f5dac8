import os
from galileo.handlers.langchain import GalileoCallback
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import HumanMessage
from langchain_google_genai import ChatGoogleGenerativeAI

llm = ChatGoogleGenerativeAI(
    model="gemini-2.5-flash",
    google_api_key="AIzaSyDejZ70xYfHqM94t9JWGvM7BDHIHOacr1A",
)
# Create a callback handler
callback = GalileoCallback(api_key="URyXd3UmDt2dWOhSE9BtEOE1YBQPA9iOBrV-msTOe6g")

# Initialize the Azure OpenAI LLM with proper credentials
# You need to set these environment variables or replace with your actual values


# Create a message with the user's query
messages = [
    HumanMessage(content="What is <PERSON><PERSON><PERSON><PERSON> and how is it used with OpenAI?")
]

# Make the API call with the callback
response = llm.invoke(messages, config={"callbacks": [callback]})

print(response.content)