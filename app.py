from galileo.handlers.langchain import GalileoCallback
from langchain_openai import ChatOpenAI
from langchain.chat_models import init_chat_model
from langchain_core.messages import HumanMessage

# Create a callback handler
callback = GalileoCallback()

# Initialize the LLM with the callback
llm = init_chat_model("azure_openai:gpt-4.1")

# Create a message with the user's query
messages = [
    HumanMessage(content="What is LangChain and how is it used with OpenAI?")
]

# Make the API call
response = llm.invoke(messages)

print(response.content)